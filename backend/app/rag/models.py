"""
文档模型定义
"""
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field as Pydantic<PERSON>ield
from .constants import Field as ConstantField
import uuid
import hashlib
from dataclasses import dataclass, field

class Document(BaseModel):
    """文档基类"""
    page_content: str
    metadata: Dict[str, Any] = PydanticField(default_factory=dict)
    source: Optional[str] = None
    doc_id: Optional[str] = None
    doc_hash: Optional[str] = None
    vector: Optional[List[float]] = None
    sparse_vector: Optional[List[float]] = None
    group_id: Optional[str] = None
    children: Optional[List['ChildDocument']] = None  # 明确指定为ChildDocument类型

    def __init__(self, **data):
        super().__init__(**data)
        if not self.doc_id:
            self.doc_id = str(uuid.uuid4())
        if not self.doc_hash:
            self.doc_hash = self._generate_hash()
        if self.source and "source" not in self.metadata:
            self.metadata["source"] = self.source

    def _generate_hash(self) -> str:
        """生成文档哈希值"""
        text = self.page_content + str(sorted(self.metadata.items()))
        return hashlib.sha256(text.encode()).hexdigest()

    def add_child(self, child_document: 'ChildDocument') -> None:
        """添加子文档

        Args:
            child_document: 要添加的子文档
        """
        if self.children is None:
            self.children = []

        # 设置父子关系
        child_document.parent_id = self.doc_id
        child_document.parent_content = self.page_content
        child_document.position = len(self.children)

        # 更新子文档元数据
        child_document.metadata.update({
            "parent_id": self.doc_id,
            "parent_content": self.page_content[:200] + "..." if len(self.page_content) > 200 else self.page_content,
            "position": child_document.position,
            "is_child": True
        })

        self.children.append(child_document)

    def remove_child(self, child_id: str) -> bool:
        """移除子文档

        Args:
            child_id: 要移除的子文档ID

        Returns:
            bool: 是否成功移除
        """
        if not self.children:
            return False

        for i, child in enumerate(self.children):
            if child.doc_id == child_id:
                self.children.pop(i)
                # 重新设置剩余子文档的位置
                for j, remaining_child in enumerate(self.children[i:], start=i):
                    remaining_child.position = j
                    remaining_child.metadata["position"] = j
                return True
        return False

    def get_child_by_id(self, child_id: str) -> Optional['ChildDocument']:
        """根据ID获取子文档

        Args:
            child_id: 子文档ID

        Returns:
            Optional[ChildDocument]: 找到的子文档，如果不存在则返回None
        """
        if not self.children:
            return None

        for child in self.children:
            if child.doc_id == child_id:
                return child
        return None

    def get_children_count(self) -> int:
        """获取子文档数量

        Returns:
            int: 子文档数量
        """
        return len(self.children) if self.children else 0

    def has_children(self) -> bool:
        """检查是否有子文档

        Returns:
            bool: 是否有子文档
        """
        return self.children is not None and len(self.children) > 0

    def get_all_content(self) -> str:
        """获取包含所有子文档的完整内容

        Returns:
            str: 完整内容
        """
        content = self.page_content
        if self.children:
            child_contents = [child.page_content for child in self.children]
            content += "\n\n" + "\n\n".join(child_contents)
        return content

    def to_point_struct(self) -> Dict[str, Any]:
        """转换为向量存储的数据结构"""
        return {
            ConstantField.PRIMARY_KEY.value: self.doc_id,
            ConstantField.VECTOR.value: self.vector,
            ConstantField.CONTENT_KEY.value: self.page_content,
            ConstantField.METADATA_KEY.value: {
                **self.metadata,
                "has_children": self.has_children(),
                "children_count": self.get_children_count()
            },
            ConstantField.GROUP_KEY.value: self.group_id or "",
            ConstantField.SPARSE_VECTOR.value: self.sparse_vector or self.vector
        }

class DocumentSegment(BaseModel):
    """文档片段"""
    id: str = PydanticField(default_factory=lambda: str(uuid.uuid4()))
    page_content: str
    metadata: Dict[str, Any] = PydanticField(default_factory=dict)
    index_node_id: Optional[str] = None
    index_node_hash: Optional[str] = None
    child_ids: Optional[List[str]] = None
    group_id: Optional[str] = None
    children: Optional[List['DocumentSegment']] = None
    embedding: Optional[List[float]] = None  # 添加 embedding 字段
    
    def __init__(self, **data):
        super().__init__(**data)
        if not self.index_node_hash:
            self.index_node_hash = self._generate_hash()
            
    def _generate_hash(self) -> str:
        """生成文档片段哈希值"""
        text = self.page_content + str(sorted(self.metadata.items()))
        return hashlib.sha256(text.encode()).hexdigest()
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，包含子文档"""
        result = {
            "id": self.id,
            "page_content": self.page_content,
            "metadata": self.metadata,
            "index_node_id": self.index_node_id,
            "index_node_hash": self.index_node_hash,
            "child_ids": self.child_ids,
            "group_id": self.group_id,
            "embedding": None  # 默认设置为 None，因为模型中没有这个字段
        }
        
        # 如果有子文档，递归转换
        if self.children:
            result["children"] = [child.to_dict() for child in self.children]
            
        return result
        
    def to_point_struct(self) -> Dict[str, Any]:
        """转换为向量存储的数据结构"""
        return {
            ConstantField.PRIMARY_KEY.value: self.id,
            ConstantField.CONTENT_KEY.value: self.page_content,
            ConstantField.METADATA_KEY.value: {
                **self.metadata,
                "index_node_hash": self.index_node_hash,
                "child_ids": self.child_ids
            },
            ConstantField.GROUP_KEY.value: self.group_id or "",
            ConstantField.VECTOR.value: self.embedding  # 添加 embedding 到输出
        }

class ChildDocument(Document):
    """子文档

    继承自Document类，添加了父子关系相关的字段和方法
    """
    parent_id: Optional[str] = None
    parent_content: Optional[str] = None
    position: Optional[int] = None

    def __init__(self, **data):
        super().__init__(**data)
        if self.parent_id and "parent_id" not in self.metadata:
            self.metadata["parent_id"] = self.parent_id

        # 确保子文档标记
        self.metadata["is_child"] = True

    def set_parent(self, parent_document: Document, position: Optional[int] = None) -> None:
        """设置父文档

        Args:
            parent_document: 父文档对象
            position: 在父文档中的位置
        """
        self.parent_id = parent_document.doc_id
        self.parent_content = parent_document.page_content
        if position is not None:
            self.position = position

        # 更新元数据
        self.metadata.update({
            "parent_id": self.parent_id,
            "parent_content": self.parent_content[:200] + "..." if len(self.parent_content) > 200 else self.parent_content,
            "position": self.position,
            "is_child": True
        })

    def get_parent_id(self) -> Optional[str]:
        """获取父文档ID

        Returns:
            Optional[str]: 父文档ID
        """
        return self.parent_id

    def get_position(self) -> Optional[int]:
        """获取在父文档中的位置

        Returns:
            Optional[int]: 位置索引
        """
        return self.position

    def is_first_child(self) -> bool:
        """检查是否是第一个子文档

        Returns:
            bool: 是否是第一个子文档
        """
        return self.position == 0

    def get_context_info(self) -> Dict[str, Any]:
        """获取上下文信息

        Returns:
            Dict[str, Any]: 包含父文档和位置信息的上下文
        """
        return {
            "parent_id": self.parent_id,
            "parent_content_preview": self.parent_content[:100] + "..." if self.parent_content and len(self.parent_content) > 100 else self.parent_content,
            "position": self.position,
            "is_child": True
        }

    def to_dict_with_parent(self) -> Dict[str, Any]:
        """转换为包含父文档信息的字典

        Returns:
            Dict[str, Any]: 包含父文档信息的字典
        """
        result = {
            "doc_id": self.doc_id,
            "page_content": self.page_content,
            "metadata": self.metadata,
            "parent_info": {
                "parent_id": self.parent_id,
                "parent_content": self.parent_content,
                "position": self.position
            }
        }
        return result

    def to_point_struct(self) -> Dict[str, Any]:
        """转换为向量存储的数据结构"""
        point = super().to_point_struct()
        point[ConstantField.METADATA_KEY.value].update({
            "parent_id": self.parent_id,
            "parent_content": self.parent_content[:200] + "..." if self.parent_content and len(self.parent_content) > 200 else self.parent_content,
            "position": self.position,
            "is_child": True
        })
        return point

class ChildChunk(BaseModel):
    """子块模型"""
    segment_id: str
    page_content: str
    vector: Optional[List[float]] = None
    metadata: Dict[str, Any] = PydanticField(default_factory=dict)
    start_pos: int = 0
    end_pos: int = 0
    group_id: Optional[str] = None
    
    def to_point_struct(self) -> Dict[str, Any]:
        """转换为向量存储的数据结构"""
        return {
            ConstantField.PRIMARY_KEY.value: self.metadata.get("chunk_id", str(uuid.uuid4())),
            ConstantField.VECTOR.value: self.vector,
            ConstantField.CONTENT_KEY.value: self.page_content,
            ConstantField.METADATA_KEY.value: {
                **self.metadata,
                "segment_id": self.segment_id,
                "start_pos": self.start_pos,
                "end_pos": self.end_pos
            },
            ConstantField.GROUP_KEY.value: self.group_id or "",
            ConstantField.SPARSE_VECTOR.value: self.vector  # 简化处理，实际应该是不同的向量
        }


class DocumentRelationshipManager:
    """文档关系管理器

    用于管理父子文档之间的关系，提供批量操作和关系维护功能
    """

    @staticmethod
    def create_parent_child_relationship(
        parent_content: str,
        child_contents: List[str],
        parent_metadata: Optional[Dict[str, Any]] = None,
        source: Optional[str] = None
    ) -> Document:
        """创建父子文档关系

        Args:
            parent_content: 父文档内容
            child_contents: 子文档内容列表
            parent_metadata: 父文档元数据
            source: 文档来源

        Returns:
            Document: 包含子文档的父文档
        """
        # 创建父文档
        parent_doc = Document(
            page_content=parent_content,
            metadata=parent_metadata or {},
            source=source
        )

        # 创建子文档
        for i, child_content in enumerate(child_contents):
            child_doc = ChildDocument(
                page_content=child_content,
                metadata=parent_metadata.copy() if parent_metadata else {},
                source=source
            )
            parent_doc.add_child(child_doc)

        return parent_doc

    @staticmethod
    def merge_documents(documents: List[Document]) -> Document:
        """合并多个文档为一个父文档

        Args:
            documents: 要合并的文档列表

        Returns:
            Document: 合并后的父文档
        """
        if not documents:
            raise ValueError("文档列表不能为空")

        # 合并内容
        merged_content = "\n\n".join([doc.page_content for doc in documents])

        # 合并元数据
        merged_metadata = documents[0].metadata.copy()
        merged_metadata["merged_from"] = [doc.doc_id for doc in documents]
        merged_metadata["original_count"] = len(documents)

        # 创建合并文档
        merged_doc = Document(
            page_content=merged_content,
            metadata=merged_metadata,
            source=documents[0].source
        )

        # 将原文档作为子文档
        for i, doc in enumerate(documents):
            child_doc = ChildDocument(
                page_content=doc.page_content,
                metadata=doc.metadata.copy(),
                source=doc.source
            )
            merged_doc.add_child(child_doc)

        return merged_doc

    @staticmethod
    def split_document_by_separator(
        document: Document,
        separator: str = "\n\n",
        min_chunk_size: int = 100
    ) -> Document:
        """按分隔符分割文档为父子结构

        Args:
            document: 要分割的文档
            separator: 分隔符
            min_chunk_size: 最小块大小

        Returns:
            Document: 分割后的父文档
        """
        # 分割内容
        chunks = document.page_content.split(separator)

        # 过滤太小的块
        valid_chunks = [chunk.strip() for chunk in chunks if len(chunk.strip()) >= min_chunk_size]

        if not valid_chunks:
            return document

        # 创建父文档（使用原内容）
        parent_doc = Document(
            page_content=document.page_content,
            metadata=document.metadata.copy(),
            source=document.source
        )

        # 创建子文档
        for chunk in valid_chunks:
            child_doc = ChildDocument(
                page_content=chunk,
                metadata=document.metadata.copy(),
                source=document.source
            )
            parent_doc.add_child(child_doc)

        return parent_doc

    @staticmethod
    def validate_parent_child_relationship(document: Document) -> List[str]:
        """验证父子文档关系的完整性

        Args:
            document: 要验证的文档

        Returns:
            List[str]: 验证错误列表，空列表表示验证通过
        """
        errors = []

        if not document.children:
            return errors

        # 检查子文档的父ID是否正确
        for i, child in enumerate(document.children):
            if child.parent_id != document.doc_id:
                errors.append(f"子文档 {i} 的parent_id不匹配")

            if child.position != i:
                errors.append(f"子文档 {i} 的position不正确")

            if not child.metadata.get("is_child"):
                errors.append(f"子文档 {i} 缺少is_child标记")

        return errors