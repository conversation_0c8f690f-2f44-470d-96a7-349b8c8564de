"""
索引处理器工厂模式

提供统一的处理器创建和管理接口，支持不同类型的索引处理器。
基于工厂模式设计，便于扩展和维护。
"""

import logging
from typing import Dict, Any, Optional, Type, Union
from enum import Enum
from abc import ABC, abstractmethod

from .index_processor import BaseIndexProcessor, ParentChildIndexProcessor
from .vector_store import MilvusVectorStore
from .document_store import DocumentStore
from .embedding_model import EmbeddingModel
from .retrieval_service import RetrievalService

logger = logging.getLogger(__name__)


class ProcessorType(str, Enum):
    """处理器类型枚举"""
    PARENT_CHILD = "parent_child"  # 父子文档处理器
    STANDARD = "standard"          # 标准文档处理器
    HIERARCHICAL = "hierarchical"  # 层次化文档处理器


class ProcessorConfig:
    """处理器配置类"""
    
    def __init__(
        self,
        processor_type: ProcessorType = ProcessorType.PARENT_CHILD,
        vector_store_config: Optional[Dict[str, Any]] = None,
        document_store_config: Optional[Dict[str, Any]] = None,
        embedding_model_config: Optional[Dict[str, Any]] = None,
        retrieval_service_config: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """初始化处理器配置
        
        Args:
            processor_type: 处理器类型
            vector_store_config: 向量存储配置
            document_store_config: 文档存储配置
            embedding_model_config: 嵌入模型配置
            retrieval_service_config: 检索服务配置
            **kwargs: 其他配置参数
        """
        self.processor_type = processor_type
        self.vector_store_config = vector_store_config or {}
        self.document_store_config = document_store_config or {}
        self.embedding_model_config = embedding_model_config or {}
        self.retrieval_service_config = retrieval_service_config or {}
        self.extra_config = kwargs
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        return {
            "processor_type": self.processor_type,
            "vector_store_config": self.vector_store_config,
            "document_store_config": self.document_store_config,
            "embedding_model_config": self.embedding_model_config,
            "retrieval_service_config": self.retrieval_service_config,
            **self.extra_config
        }


class IProcessorFactory(ABC):
    """处理器工厂接口"""
    
    @abstractmethod
    def create_processor(
        self,
        processor_type: ProcessorType,
        config: Optional[ProcessorConfig] = None
    ) -> BaseIndexProcessor:
        """创建处理器
        
        Args:
            processor_type: 处理器类型
            config: 处理器配置
            
        Returns:
            BaseIndexProcessor: 创建的处理器实例
        """
        pass
    
    @abstractmethod
    def get_supported_types(self) -> list[ProcessorType]:
        """获取支持的处理器类型
        
        Returns:
            list[ProcessorType]: 支持的处理器类型列表
        """
        pass


class IndexProcessorFactory(IProcessorFactory):
    """索引处理器工厂
    
    实现工厂模式，负责创建和管理不同类型的索引处理器。
    支持处理器的注册、创建、配置和生命周期管理。
    """
    
    def __init__(self):
        """初始化工厂"""
        self._processors: Dict[str, Type[BaseIndexProcessor]] = {}
        self._instances: Dict[str, BaseIndexProcessor] = {}
        self._register_default_processors()
    
    def _register_default_processors(self):
        """注册默认处理器"""
        self.register_processor(ProcessorType.PARENT_CHILD, ParentChildIndexProcessor)
        logger.info("已注册默认处理器")
    
    def register_processor(
        self,
        processor_type: ProcessorType,
        processor_class: Type[BaseIndexProcessor]
    ) -> None:
        """注册处理器类型
        
        Args:
            processor_type: 处理器类型
            processor_class: 处理器类
        """
        if not issubclass(processor_class, BaseIndexProcessor):
            raise ValueError(f"处理器类必须继承自BaseIndexProcessor: {processor_class}")
        
        self._processors[processor_type.value] = processor_class
        logger.info(f"已注册处理器类型: {processor_type.value}")
    
    def unregister_processor(self, processor_type: ProcessorType) -> None:
        """注销处理器类型
        
        Args:
            processor_type: 处理器类型
        """
        if processor_type.value in self._processors:
            del self._processors[processor_type.value]
            logger.info(f"已注销处理器类型: {processor_type.value}")
        
        # 清理实例缓存
        if processor_type.value in self._instances:
            del self._instances[processor_type.value]
    
    def create_processor(
        self,
        processor_type: ProcessorType,
        config: Optional[ProcessorConfig] = None
    ) -> BaseIndexProcessor:
        """创建处理器实例
        
        Args:
            processor_type: 处理器类型
            config: 处理器配置
            
        Returns:
            BaseIndexProcessor: 创建的处理器实例
            
        Raises:
            ValueError: 不支持的处理器类型
            Exception: 处理器创建失败
        """
        try:
            logger.info(f"开始创建处理器: {processor_type.value}")
            
            if processor_type.value not in self._processors:
                raise ValueError(f"不支持的处理器类型: {processor_type.value}")
            
            processor_class = self._processors[processor_type.value]
            
            # 准备初始化参数
            init_kwargs = self._prepare_init_kwargs(config)
            
            # 创建处理器实例
            processor = processor_class(**init_kwargs)
            
            logger.info(f"成功创建处理器: {processor_type.value}")
            return processor
            
        except Exception as e:
            logger.error(f"创建处理器失败 {processor_type.value}: {str(e)}")
            raise
    
    def get_or_create_processor(
        self,
        processor_type: ProcessorType,
        config: Optional[ProcessorConfig] = None,
        use_cache: bool = True
    ) -> BaseIndexProcessor:
        """获取或创建处理器实例（支持缓存）
        
        Args:
            processor_type: 处理器类型
            config: 处理器配置
            use_cache: 是否使用缓存
            
        Returns:
            BaseIndexProcessor: 处理器实例
        """
        cache_key = processor_type.value
        
        if use_cache and cache_key in self._instances:
            logger.info(f"使用缓存的处理器实例: {processor_type.value}")
            return self._instances[cache_key]
        
        # 创建新实例
        processor = self.create_processor(processor_type, config)
        
        if use_cache:
            self._instances[cache_key] = processor
            logger.info(f"已缓存处理器实例: {processor_type.value}")
        
        return processor
    
    def _prepare_init_kwargs(self, config: Optional[ProcessorConfig]) -> Dict[str, Any]:
        """准备处理器初始化参数
        
        Args:
            config: 处理器配置
            
        Returns:
            Dict[str, Any]: 初始化参数
        """
        if not config:
            return {}
        
        init_kwargs = {}
        
        # 创建向量存储
        if config.vector_store_config:
            try:
                vector_store = MilvusVectorStore(**config.vector_store_config)
                init_kwargs["vector_store"] = vector_store
            except Exception as e:
                logger.warning(f"创建向量存储失败: {str(e)}")
        
        # 创建文档存储
        if config.document_store_config:
            try:
                document_store = DocumentStore(**config.document_store_config)
                init_kwargs["document_store"] = document_store
            except Exception as e:
                logger.warning(f"创建文档存储失败: {str(e)}")
        
        # 创建嵌入模型
        if config.embedding_model_config:
            try:
                embedding_model = EmbeddingModel(**config.embedding_model_config)
                init_kwargs["embedding_model"] = embedding_model
            except Exception as e:
                logger.warning(f"创建嵌入模型失败: {str(e)}")
        
        # 创建检索服务
        if config.retrieval_service_config:
            try:
                retrieval_service = RetrievalService(**config.retrieval_service_config)
                init_kwargs["retrieval_service"] = retrieval_service
            except Exception as e:
                logger.warning(f"创建检索服务失败: {str(e)}")
        
        # 添加额外配置
        init_kwargs.update(config.extra_config)
        
        return init_kwargs
    
    def get_supported_types(self) -> list[ProcessorType]:
        """获取支持的处理器类型
        
        Returns:
            list[ProcessorType]: 支持的处理器类型列表
        """
        return [ProcessorType(type_name) for type_name in self._processors.keys()]
    
    def clear_cache(self) -> None:
        """清理处理器实例缓存"""
        self._instances.clear()
        logger.info("已清理处理器实例缓存")
    
    def get_processor_info(self) -> Dict[str, Any]:
        """获取处理器工厂信息
        
        Returns:
            Dict[str, Any]: 工厂信息
        """
        return {
            "supported_types": [ptype.value for ptype in self.get_supported_types()],
            "registered_processors": list(self._processors.keys()),
            "cached_instances": list(self._instances.keys()),
            "total_registered": len(self._processors),
            "total_cached": len(self._instances)
        }


# 全局工厂实例
_global_factory: Optional[IndexProcessorFactory] = None


def get_processor_factory() -> IndexProcessorFactory:
    """获取全局处理器工厂实例
    
    Returns:
        IndexProcessorFactory: 工厂实例
    """
    global _global_factory
    if _global_factory is None:
        _global_factory = IndexProcessorFactory()
    return _global_factory


def create_processor(
    processor_type: Union[ProcessorType, str],
    config: Optional[ProcessorConfig] = None
) -> BaseIndexProcessor:
    """便捷函数：创建处理器
    
    Args:
        processor_type: 处理器类型
        config: 处理器配置
        
    Returns:
        BaseIndexProcessor: 处理器实例
    """
    if isinstance(processor_type, str):
        processor_type = ProcessorType(processor_type)
    
    factory = get_processor_factory()
    return factory.create_processor(processor_type, config)
