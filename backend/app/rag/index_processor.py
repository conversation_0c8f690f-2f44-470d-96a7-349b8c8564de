"""
父子文档索引处理器

基于参考项目Dify的实现，提供专门的父子文档处理架构，包括文档提取、转换、加载、清理和检索功能。
"""

import os
import uuid
import logging
from typing import List, Dict, Any, Optional, Union
from enum import Enum
from abc import ABC, abstractmethod

from .models import Document, ChildDocument
from .extractor.extract_processor import ExtractProcessor, ExtractMode
from .cleaner.clean_processor import CleanProcessor, CleanLevel
from .document_splitter import ParentChildDocumentSplitter, Rule, SplitMode
from .vector_store import MilvusVectorStore
from .document_store import DocumentStore
from .retrieval_service import RetrievalService
from .embedding_model import EmbeddingModel
from .config import get_document_process_rule, get_embedding_model_config

logger = logging.getLogger(__name__)


class ParentMode(str, Enum):
    """父文档分割模式"""
    PARAGRAPH = "paragraph"  # 段落模式
    FULL_DOC = "full_doc"    # 全文档模式


class ExtractSetting:
    """文档提取设置"""
    
    def __init__(
        self,
        file_path: str,
        extract_mode: str = ExtractMode.BASIC,
        cache_key: Optional[str] = None,
        **kwargs
    ):
        self.file_path = file_path
        self.extract_mode = extract_mode
        self.cache_key = cache_key
        self.kwargs = kwargs


class ProcessRule:
    """处理规则"""
    
    def __init__(
        self,
        mode: str = "automatic",
        parent_mode: ParentMode = ParentMode.PARAGRAPH,
        rules: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        self.mode = mode
        self.parent_mode = parent_mode
        self.rules = rules or {}
        self.kwargs = kwargs
        
        # 设置默认规则
        if not self.rules:
            self.rules = self._get_default_rules()
    
    def _get_default_rules(self) -> Dict[str, Any]:
        """获取默认处理规则"""
        return {
            "segmentation": {
                "separator": "\n\n",
                "max_tokens": 1024,
                "chunk_overlap": 200
            },
            "subchunk_segmentation": {
                "separator": "\n",
                "max_tokens": 512,
                "chunk_overlap": 50
            },
            "pre_processing_rules": [
                {"type": "remove_html", "enabled": True},
                {"type": "remove_extra_spaces", "enabled": True},
                {"type": "remove_urls", "enabled": False}
            ]
        }


class BaseIndexProcessor(ABC):
    """索引处理器基类"""
    
    @abstractmethod
    def extract(self, extract_setting: ExtractSetting, **kwargs) -> List[Document]:
        """提取文档"""
        pass
    
    @abstractmethod
    def transform(self, documents: List[Document], **kwargs) -> List[Document]:
        """转换文档为父子结构"""
        pass
    
    @abstractmethod
    def load(self, documents: List[Document], **kwargs) -> None:
        """加载文档到存储系统"""
        pass
    
    @abstractmethod
    def clean(self, node_ids: Optional[List[str]] = None, **kwargs) -> None:
        """清理文档"""
        pass
    
    @abstractmethod
    def retrieve(
        self,
        query: str,
        top_k: int = 10,
        score_threshold: float = 0.0,
        **kwargs
    ) -> List[Document]:
        """检索文档"""
        pass


class ParentChildIndexProcessor(BaseIndexProcessor):
    """父子文档索引处理器
    
    实现专门的父子文档处理架构，支持：
    - 文档提取和清理
    - 父子结构转换
    - 预览模式和批量处理
    - 向量存储和检索
    """
    
    def __init__(
        self,
        vector_store: Optional[MilvusVectorStore] = None,
        document_store: Optional[DocumentStore] = None,
        embedding_model: Optional[EmbeddingModel] = None,
        retrieval_service: Optional[RetrievalService] = None
    ):
        """初始化处理器
        
        Args:
            vector_store: 向量存储实例
            document_store: 文档存储实例
            embedding_model: 嵌入模型实例
            retrieval_service: 检索服务实例
        """
        self.vector_store = vector_store
        self.document_store = document_store
        self.embedding_model = embedding_model
        self.retrieval_service = retrieval_service
        
        # 初始化组件
        self._init_components()
    
    def _init_components(self):
        """初始化组件"""
        try:
            # 初始化向量存储
            if not self.vector_store:
                self.vector_store = MilvusVectorStore()
            
            # 初始化文档存储
            if not self.document_store:
                self.document_store = DocumentStore()
            
            # 初始化嵌入模型
            if not self.embedding_model:
                config = get_embedding_model_config()
                self.embedding_model = EmbeddingModel(
                    model_name=config.get("model_name"),
                    api_base=config.get("api_base")
                )
            
            # 初始化检索服务
            if not self.retrieval_service:
                self.retrieval_service = RetrievalService(
                    vector_store=self.vector_store,
                    document_store=self.document_store,
                    embedding_model=self.embedding_model
                )
                
        except Exception as e:
            logger.warning(f"组件初始化失败，某些功能可能不可用: {str(e)}")

    def extract(self, extract_setting: ExtractSetting, **kwargs) -> List[Document]:
        """提取文档

        Args:
            extract_setting: 提取设置
            **kwargs: 额外参数，包括：
                - process_rule_mode: 处理规则模式
                - is_automatic: 是否自动模式

        Returns:
            List[Document]: 提取的文档列表
        """
        try:
            logger.info(f"开始提取文档: {extract_setting.file_path}")

            # 根据文件类型选择提取方法
            file_ext = os.path.splitext(extract_setting.file_path)[1].lower()

            if file_ext == '.pdf':
                # 使用ExtractProcessor提取PDF文档
                documents = ExtractProcessor.extract_pdf(
                    file_path=extract_setting.file_path,
                    mode=extract_setting.extract_mode,
                    cache_key=extract_setting.cache_key
                )
            else:
                # 处理文本文件（.txt, .md等）
                logger.info(f"处理文本文件: {extract_setting.file_path}")
                with open(extract_setting.file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 创建文档对象
                document = Document(
                    page_content=content,
                    metadata={
                        "source": extract_setting.file_path,
                        "file_type": file_ext,
                        "cache_key": extract_setting.cache_key
                    }
                )
                documents = [document]

            logger.info(f"文档提取完成，共提取 {len(documents)} 个文档片段")
            return documents

        except Exception as e:
            logger.error(f"文档提取失败: {str(e)}")
            raise

    def transform(self, documents: List[Document], **kwargs) -> List[Document]:
        """转换文档为父子结构

        Args:
            documents: 要转换的文档列表
            **kwargs: 额外参数，包括：
                - process_rule: 处理规则
                - preview: 是否预览模式
                - embedding_model_instance: 嵌入模型实例

        Returns:
            List[Document]: 转换后的父子文档列表
        """
        try:
            logger.info(f"开始转换文档为父子结构，输入文档数量: {len(documents)}")

            # 获取处理规则
            process_rule_dict = kwargs.get("process_rule")
            if not process_rule_dict:
                # 使用默认规则
                process_rule_dict = {
                    "mode": "automatic",
                    "rules": get_document_process_rule()["rules"]
                }

            # 从process_rule_dict中获取parent_mode
            parent_mode_str = process_rule_dict.get("parent_mode", "paragraph")
            if isinstance(parent_mode_str, str):
                parent_mode = ParentMode.FULL_DOC if parent_mode_str == "full_doc" else ParentMode.PARAGRAPH
            else:
                parent_mode = parent_mode_str

            process_rule = ProcessRule(
                mode=process_rule_dict.get("mode", "automatic"),
                parent_mode=parent_mode,
                rules=process_rule_dict.get("rules", {})
            )

            # 是否预览模式
            preview_mode = kwargs.get("preview", False)

            all_documents = []

            if process_rule.parent_mode == ParentMode.PARAGRAPH:
                # 段落模式处理
                # 从kwargs中移除process_rule以避免参数冲突
                filtered_kwargs = {k: v for k, v in kwargs.items() if k != 'process_rule'}
                all_documents = self._transform_paragraph_mode(
                    documents, process_rule, preview_mode, **filtered_kwargs
                )
            elif process_rule.parent_mode == ParentMode.FULL_DOC:
                # 全文档模式处理
                filtered_kwargs = {k: v for k, v in kwargs.items() if k != 'process_rule'}
                all_documents = self._transform_full_doc_mode(
                    documents, process_rule, preview_mode, **filtered_kwargs
                )

            logger.info(f"文档转换完成，生成 {len(all_documents)} 个父文档")
            return all_documents

        except Exception as e:
            logger.error(f"文档转换失败: {str(e)}")
            raise

    def _transform_paragraph_mode(
        self,
        documents: List[Document],
        process_rule: ProcessRule,
        preview_mode: bool,
        **kwargs
    ) -> List[Document]:
        """段落模式转换

        Args:
            documents: 输入文档列表
            process_rule: 处理规则
            preview_mode: 是否预览模式
            **kwargs: 额外参数

        Returns:
            List[Document]: 转换后的文档列表
        """
        all_documents = []

        # 获取分割规则
        segmentation = process_rule.rules.get("segmentation", {})
        subchunk_segmentation = process_rule.rules.get("subchunk_segmentation", {})

        # 创建父文档分割规则
        parent_rule = Rule(
            mode=SplitMode.PARENT_CHILD,
            max_tokens=segmentation.get("max_tokens", 1024),
            chunk_overlap=segmentation.get("chunk_overlap", 200),
            fixed_separator=segmentation.get("separator", "\n\n"),
            subchunk_max_tokens=subchunk_segmentation.get("max_tokens", 512),
            subchunk_overlap=subchunk_segmentation.get("chunk_overlap", 50),
            subchunk_separator=subchunk_segmentation.get("separator", "\n"),
            clean_text=True,
            keep_separator=True
        )

        # 创建分割器
        splitter = ParentChildDocumentSplitter()

        for document in documents:
            if preview_mode and len(all_documents) >= 10:
                logger.info("预览模式：已达到最大文档数量限制")
                break

            # 清理文档内容
            cleaned_content = CleanProcessor.clean(
                document.page_content,
                level=CleanLevel.FULL
            )
            document.page_content = cleaned_content

            # 分割文档
            segments = splitter.split_documents([document], parent_rule)

            # 处理分割结果
            for segment in segments:
                if segment.page_content.strip():
                    # 生成文档ID和哈希
                    doc_id = str(uuid.uuid4())
                    doc_hash = self._generate_text_hash(segment.page_content)

                    # 更新元数据
                    segment.metadata.update({
                        "doc_id": doc_id,
                        "doc_hash": doc_hash
                    })

                    # 处理内容格式
                    page_content = segment.page_content
                    if page_content.startswith(".") or page_content.startswith("。"):
                        page_content = page_content[1:].strip()

                    if len(page_content) > 0:
                        segment.page_content = page_content

                        # 生成子文档
                        child_documents = self._split_child_documents(
                            segment, process_rule, **kwargs
                        )
                        segment.children = child_documents

                        all_documents.append(segment)

        return all_documents

    def _transform_full_doc_mode(
        self,
        documents: List[Document],
        process_rule: ProcessRule,
        preview_mode: bool,
        **kwargs
    ) -> List[Document]:
        """全文档模式转换

        Args:
            documents: 输入文档列表
            process_rule: 处理规则
            preview_mode: 是否预览模式
            **kwargs: 额外参数

        Returns:
            List[Document]: 转换后的文档列表
        """
        # 合并所有文档内容
        page_content = "\n".join([doc.page_content for doc in documents])

        # 创建合并文档
        merged_document = Document(
            page_content=page_content,
            metadata=documents[0].metadata if documents else {}
        )

        # 生成子文档
        child_documents = self._split_child_documents(
            merged_document, process_rule, **kwargs
        )

        # 预览模式限制子文档数量
        if preview_mode and len(child_documents) > 10:
            child_documents = child_documents[:10]
            logger.info("预览模式：限制子文档数量为10个")

        # 设置文档元数据
        doc_id = str(uuid.uuid4())
        doc_hash = self._generate_text_hash(merged_document.page_content)
        merged_document.metadata.update({
            "doc_id": doc_id,
            "doc_hash": doc_hash
        })
        merged_document.children = child_documents

        return [merged_document]

    def _split_child_documents(
        self,
        parent_document: Document,
        process_rule: ProcessRule,
        **kwargs
    ) -> List[ChildDocument]:
        """分割子文档

        Args:
            parent_document: 父文档
            process_rule: 处理规则
            **kwargs: 额外参数

        Returns:
            List[ChildDocument]: 子文档列表
        """
        try:
            # 获取子块分割规则
            subchunk_segmentation = process_rule.rules.get("subchunk_segmentation", {})

            # 创建子文档分割规则
            child_rule = Rule(
                mode=SplitMode.PARAGRAPH,  # 子文档使用段落模式
                max_tokens=subchunk_segmentation.get("max_tokens", 512),
                chunk_overlap=subchunk_segmentation.get("chunk_overlap", 50),
                fixed_separator=subchunk_segmentation.get("separator", "\n"),
                clean_text=True,
                keep_separator=False
            )

            # 创建分割器
            from .text_splitter import FixedRecursiveCharacterTextSplitter
            child_splitter = FixedRecursiveCharacterTextSplitter(
                chunk_size=child_rule.max_tokens,
                chunk_overlap=child_rule.chunk_overlap,
                separators=[child_rule.fixed_separator]
            )

            # 分割子文档
            child_nodes = child_splitter.split_documents([parent_document])
            child_documents = []

            for i, child_node in enumerate(child_nodes):
                if child_node.page_content.strip():
                    # 生成子文档ID和哈希
                    doc_id = str(uuid.uuid4())
                    doc_hash = self._generate_text_hash(child_node.page_content)

                    # 创建子文档
                    child_document = ChildDocument(
                        page_content=child_node.page_content,
                        metadata=parent_document.metadata.copy(),
                        parent_id=parent_document.metadata.get("doc_id"),
                        parent_content=parent_document.page_content,
                        position=i
                    )

                    # 更新子文档元数据
                    child_document.metadata.update({
                        "doc_id": doc_id,
                        "doc_hash": doc_hash,
                        "is_child": True,
                        "parent_id": parent_document.metadata.get("doc_id"),
                        "position": i
                    })

                    # 处理内容格式
                    child_page_content = child_document.page_content
                    if child_page_content.startswith(".") or child_page_content.startswith("。"):
                        child_page_content = child_page_content[1:].strip()

                    if len(child_page_content) > 0:
                        child_document.page_content = child_page_content
                        child_documents.append(child_document)

            logger.info(f"生成 {len(child_documents)} 个子文档")
            return child_documents

        except Exception as e:
            logger.error(f"分割子文档失败: {str(e)}")
            return []

    def _generate_text_hash(self, text: str) -> str:
        """生成文本哈希值

        Args:
            text: 要生成哈希的文本

        Returns:
            str: 哈希值
        """
        import hashlib
        return hashlib.sha256(text.encode()).hexdigest()[:16]

    def load(self, documents: List[Document], **kwargs) -> None:
        """加载文档到存储系统

        Args:
            documents: 要加载的文档列表
            **kwargs: 额外参数，包括：
                - with_keywords: 是否包含关键词
                - collection_name: 集合名称

        Raises:
            Exception: 加载失败时抛出异常
        """
        try:
            logger.info(f"开始加载 {len(documents)} 个文档到存储系统")

            with_keywords = kwargs.get("with_keywords", True)
            collection_name = kwargs.get("collection_name")

            # 设置集合名称
            if collection_name and hasattr(self.vector_store, 'set_collection'):
                self.vector_store.set_collection(collection_name)

            # 处理每个父文档
            for document in documents:
                child_documents = document.children
                if child_documents:
                    # 准备子文档数据
                    child_docs = []
                    child_vectors = []

                    for child_doc in child_documents:
                        # 转换为标准Document格式
                        formatted_child = Document(
                            page_content=child_doc.page_content,
                            metadata=child_doc.metadata,
                            doc_id=child_doc.doc_id,
                            doc_hash=child_doc.doc_hash
                        )
                        child_docs.append(formatted_child)

                        # 生成向量嵌入
                        if self.embedding_model:
                            try:
                                vector = self.embedding_model.embed_text(child_doc.page_content)
                                child_vectors.append(vector)
                            except Exception as e:
                                logger.warning(f"生成向量嵌入失败: {str(e)}")
                                # 使用零向量作为占位符
                                child_vectors.append([0.0] * 768)  # 假设768维
                        else:
                            child_vectors.append([0.0] * 768)

                    # 存储子文档到向量数据库
                    if child_docs and child_vectors:
                        self.vector_store.insert(child_docs, child_vectors)
                        logger.info(f"成功存储 {len(child_docs)} 个子文档到向量数据库")

                    # 存储父文档到文档数据库
                    if self.document_store:
                        try:
                            # 这里可以添加文档存储逻辑
                            pass
                        except Exception as e:
                            logger.warning(f"存储父文档到文档数据库失败: {str(e)}")

            logger.info("文档加载完成")

        except Exception as e:
            logger.error(f"文档加载失败: {str(e)}")
            raise

    def clean(self, node_ids: Optional[List[str]] = None, **kwargs) -> None:
        """清理文档

        Args:
            node_ids: 要清理的节点ID列表，如果为None则清理所有
            **kwargs: 额外参数，包括：
                - delete_child_chunks: 是否删除子块
                - collection_name: 集合名称

        Raises:
            Exception: 清理失败时抛出异常
        """
        try:
            logger.info(f"开始清理文档，节点ID: {node_ids}")

            delete_child_chunks = kwargs.get("delete_child_chunks", False)
            collection_name = kwargs.get("collection_name")

            # 设置集合名称
            if collection_name and hasattr(self.vector_store, 'set_collection'):
                self.vector_store.set_collection(collection_name)

            if node_ids:
                # 清理指定节点
                if self.vector_store:
                    self.vector_store.delete_by_ids(node_ids)
                    logger.info(f"成功从向量数据库删除 {len(node_ids)} 个节点")

                # 清理文档数据库中的相关记录
                if self.document_store and delete_child_chunks:
                    try:
                        # 这里可以添加文档数据库清理逻辑
                        pass
                    except Exception as e:
                        logger.warning(f"清理文档数据库失败: {str(e)}")
            else:
                # 清理所有文档
                if self.vector_store:
                    self.vector_store.delete_all()
                    logger.info("成功清理向量数据库中的所有文档")

                if self.document_store and delete_child_chunks:
                    try:
                        # 这里可以添加文档数据库清理逻辑
                        pass
                    except Exception as e:
                        logger.warning(f"清理文档数据库失败: {str(e)}")

            logger.info("文档清理完成")

        except Exception as e:
            logger.error(f"文档清理失败: {str(e)}")
            raise

    def retrieve(
        self,
        query: str,
        top_k: int = 10,
        score_threshold: float = 0.0,
        **kwargs
    ) -> List[Document]:
        """检索文档

        Args:
            query: 查询文本
            top_k: 返回结果数量
            score_threshold: 分数阈值
            **kwargs: 额外参数，包括：
                - retrieval_method: 检索方法
                - reranking_model: 重排序模型
                - collection_name: 集合名称

        Returns:
            List[Document]: 检索结果文档列表

        Raises:
            Exception: 检索失败时抛出异常
        """
        try:
            logger.info(f"开始检索文档，查询: {query[:50]}...")

            retrieval_method = kwargs.get("retrieval_method", "vector")
            reranking_model = kwargs.get("reranking_model", {})
            collection_name = kwargs.get("collection_name")

            # 设置集合名称
            if collection_name and hasattr(self.vector_store, 'set_collection'):
                self.vector_store.set_collection(collection_name)

            # 使用检索服务进行检索
            if self.retrieval_service:
                results = self.retrieval_service.search(
                    query=query,
                    top_k=top_k,
                    score_threshold=score_threshold,
                    search_type=retrieval_method
                )
            else:
                # 直接使用向量存储进行检索
                if not self.embedding_model:
                    raise ValueError("嵌入模型未初始化，无法进行向量检索")

                # 生成查询向量
                query_vector = self.embedding_model.embed_text(query)

                # 执行向量搜索
                results = self.vector_store.search(
                    query_vector=query_vector,
                    top_k=top_k,
                    score_threshold=score_threshold
                )

            # 组织检索结果
            docs = []
            for result in results:
                # 检查分数阈值
                if hasattr(result, 'score') and result.score > score_threshold:
                    # 构建文档对象
                    metadata = getattr(result, 'metadata', {})
                    metadata["score"] = getattr(result, 'score', 0.0)

                    doc = Document(
                        page_content=getattr(result, 'page_content', ''),
                        metadata=metadata
                    )
                    docs.append(doc)
                elif not hasattr(result, 'score'):
                    # 如果结果没有分数字段，直接添加
                    doc = Document(
                        page_content=getattr(result, 'page_content', ''),
                        metadata=getattr(result, 'metadata', {})
                    )
                    docs.append(doc)

            logger.info(f"检索完成，返回 {len(docs)} 个结果")
            return docs

        except Exception as e:
            logger.error(f"文档检索失败: {str(e)}")
            raise

    def batch_process(
        self,
        extract_settings: List[ExtractSetting],
        process_rule: Optional[ProcessRule] = None,
        preview_mode: bool = False,
        **kwargs
    ) -> List[Document]:
        """批量处理文档

        Args:
            extract_settings: 提取设置列表
            process_rule: 处理规则
            preview_mode: 是否预览模式
            **kwargs: 额外参数

        Returns:
            List[Document]: 处理后的文档列表
        """
        try:
            logger.info(f"开始批量处理 {len(extract_settings)} 个文档")

            all_documents = []

            for extract_setting in extract_settings:
                try:
                    # 提取文档
                    documents = self.extract(extract_setting, **kwargs)

                    # 转换文档
                    transformed_docs = self.transform(
                        documents,
                        process_rule=process_rule.__dict__ if process_rule else None,
                        preview=preview_mode,
                        **kwargs
                    )

                    all_documents.extend(transformed_docs)

                    # 预览模式限制
                    if preview_mode and len(all_documents) >= 50:
                        logger.info("预览模式：已达到批量处理文档数量限制")
                        break

                except Exception as e:
                    logger.error(f"处理文档失败 {extract_setting.file_path}: {str(e)}")
                    continue

            logger.info(f"批量处理完成，共处理 {len(all_documents)} 个文档")
            return all_documents

        except Exception as e:
            logger.error(f"批量处理失败: {str(e)}")
            raise
