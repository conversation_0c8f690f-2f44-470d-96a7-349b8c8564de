# RAG父子文档分割功能改进计划

## 项目概述
基于对当前项目与参考项目（Dify）的对比分析，制定详细的RAG父子文档处理功能改进计划。

## 主要差异总结

### 1. 架构设计差异
- **参考项目**：专门的索引处理器架构，工厂模式管理
- **当前项目**：通用分割器，缺少专门的索引处理架构

### 2. 文档分割策略差异
- **参考项目**：支持PARAGRAPH和FULL_DOC两种模式，灵活配置
- **当前项目**：仅支持段落模式，配置相对固定

### 3. 存储结构差异
- **参考项目**：关系型数据库，完善的批量操作
- **当前项目**：MongoDB存储，批量操作支持有限

### 4. 检索策略差异
- **参考项目**：多种检索方法，完整重排序机制
- **当前项目**：主要依赖向量搜索，缺少重排序

## 详细改进任务列表

### 优先级1：核心架构改进

#### 任务1.1：创建专门的父子文档索引处理器
- **文件路径**：`backend/app/rag/index_processor.py`
- **修改内容**：
  - 创建 `ParentChildIndexProcessor` 类
  - 实现 `extract`, `transform`, `load`, `clean`, `retrieve` 方法
  - 支持预览模式和批量处理
- **预期效果**：提供专门的父子文档处理架构
- **测试方案**：单元测试验证各方法功能

#### 任务1.2：增强文档模型
- **文件路径**：`backend/app/rag/models.py`
- **修改内容**：
  - 创建 `ChildDocument` 模型类
  - 增强 `Document` 类支持子文档列表
  - 添加父子关系管理方法
- **预期效果**：更清晰的父子文档数据结构
- **测试方案**：模型创建和关系维护测试

#### 任务1.3：实现工厂模式管理
- **文件路径**：`backend/app/rag/processor_factory.py`
- **修改内容**：
  - 创建 `IndexProcessorFactory` 类
  - 支持不同类型处理器的创建和管理
  - 提供统一的处理器接口
- **预期效果**：灵活的处理器管理机制
- **测试方案**：工厂模式创建测试

### 优先级2：分割策略增强

#### 任务2.1：支持多种父文档模式
- **文件路径**：`backend/app/rag/document_splitter.py`
- **修改内容**：
  - 添加 `ParentMode.FULL_DOC` 支持
  - 实现全文档作为父文档的分割逻辑
  - 优化段落模式的分割算法
- **预期效果**：更灵活的父文档创建策略
- **测试方案**：不同模式的分割效果测试

#### 任务2.2：增强分割规则配置
- **文件路径**：`backend/app/rag/models.py`
- **修改内容**：
  - 扩展 `Rule` 类支持更多配置选项
  - 添加预览模式配置
  - 支持动态分割参数调整
- **预期效果**：更灵活的分割配置
- **测试方案**：配置参数验证测试

#### 任务2.3：实现预览模式优化
- **文件路径**：`backend/app/rag/document_splitter.py`
- **修改内容**：
  - 添加预览模式支持
  - 限制预览时的子块数量
  - 优化预览性能
- **预期效果**：提升预览体验和性能
- **测试方案**：预览模式功能测试

### 优先级3：检索功能增强

#### 任务3.1：实现多种检索方法
- **文件路径**：`backend/app/rag/retrieval_service.py`
- **修改内容**：
  - 添加关键词搜索支持
  - 实现全文搜索功能
  - 支持混合检索策略
- **预期效果**：更丰富的检索选项
- **测试方案**：不同检索方法效果测试

#### 任务3.2：实现重排序机制
- **文件路径**：`backend/app/rag/rerank_processor.py`
- **修改内容**：
  - 创建 `DataPostProcessor` 类
  - 实现基于相关性的重排序
  - 支持多种重排序算法
- **预期效果**：提升检索结果质量
- **测试方案**：重排序效果评估测试

#### 任务3.3：优化并行检索
- **文件路径**：`backend/app/rag/retrieval_service.py`
- **修改内容**：
  - 实现多线程并行检索
  - 优化检索性能
  - 添加异常处理机制
- **预期效果**：提升检索性能和稳定性
- **测试方案**：并发性能测试

### 优先级4：存储优化

#### 任务4.1：优化批量操作
- **文件路径**：`backend/app/rag/document_store.py`
- **修改内容**：
  - 实现批量插入优化
  - 添加事务管理支持
  - 优化查询性能
- **预期效果**：提升存储操作性能
- **测试方案**：批量操作性能测试

#### 任务4.2：增强索引管理
- **文件路径**：`backend/app/rag/vector_store.py`
- **修改内容**：
  - 优化向量索引配置
  - 添加索引维护功能
  - 支持动态索引调整
- **预期效果**：更好的向量检索性能
- **测试方案**：索引性能测试

### 优先级5：API接口优化

#### 任务5.1：统一API接口
- **文件路径**：`backend/app/api/v1/endpoints/rag.py`
- **修改内容**：
  - 统一父子文档处理接口
  - 添加处理模式选择参数
  - 优化错误处理和响应格式
- **预期效果**：更一致的API体验
- **测试方案**：API接口功能测试

## 实施计划

### 第一阶段（1-2天）：核心架构改进
- 完成任务1.1-1.3
- 建立基础的处理器架构

### 第二阶段（2-3天）：分割策略增强
- 完成任务2.1-2.3
- 提升文档分割灵活性

### 第三阶段（2-3天）：检索功能增强
- 完成任务3.1-3.3
- 实现多样化检索策略

### 第四阶段（1-2天）：存储和API优化
- 完成任务4.1-5.1
- 整体性能和体验优化

### 第五阶段（1天）：测试和文档
- 完整的功能测试
- 更新相关文档

## 风险评估

### 高风险项
- 架构重构可能影响现有功能
- 数据库结构变更需要迁移策略

### 中风险项
- 新增检索方法的性能影响
- API接口变更的兼容性

### 低风险项
- 配置参数扩展
- 文档和测试更新

## 成功标准

1. **功能完整性**：支持参考项目的主要功能特性
2. **性能提升**：检索和处理性能有明显改善
3. **代码质量**：测试覆盖率达到70%以上
4. **兼容性**：保持与现有系统的兼容性
5. **文档完善**：提供完整的使用和维护文档

## 后续维护

1. **监控指标**：建立性能和质量监控
2. **持续优化**：根据使用反馈持续改进
3. **版本管理**：建立清晰的版本发布流程

## 依赖关系图

```
任务1.1 → 任务1.2 → 任务1.3
    ↓        ↓        ↓
任务2.1 → 任务2.2 → 任务2.3
    ↓        ↓        ↓
任务3.1 → 任务3.2 → 任务3.3
    ↓        ↓        ↓
任务4.1 → 任务4.2 → 任务5.1
```

## 关键技术点

### 1. 父子文档关系管理
- 使用UUID确保唯一性
- 维护清晰的父子关系映射
- 支持批量关系操作

### 2. 向量索引优化
- 采用HNSW索引提升检索性能
- 支持动态索引参数调整
- 实现索引的增量更新

### 3. 检索策略多样化
- 语义搜索：基于向量相似度
- 关键词搜索：基于文本匹配
- 全文搜索：基于倒排索引
- 混合搜索：多策略结合

### 4. 重排序算法
- 基于相关性评分的重排序
- 支持多种重排序模型
- 可配置的重排序参数

## 测试策略

### 单元测试
- 每个新增类和方法的单元测试
- 覆盖正常流程和异常情况
- 模拟依赖项进行隔离测试

### 集成测试
- 端到端的文档处理流程测试
- API接口的集成测试
- 数据库操作的集成测试

### 性能测试
- 大量文档的处理性能测试
- 并发检索的性能测试
- 内存和CPU使用率监控

### 兼容性测试
- 与现有功能的兼容性验证
- 不同配置参数的兼容性测试
- 数据迁移的兼容性验证